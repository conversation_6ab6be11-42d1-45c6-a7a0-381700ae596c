---
model: sonnet
---

# <PERSON><PERSON><PERSON><PERSON>/LangGraph 智能体脚手架

Create a production-ready <PERSON>C<PERSON><PERSON>/LangGraph agent for: $ARGUMENTS

Implement a complete agent system including:

1. **Agent Architecture**:
   - LangGraph state machine
   - Tool selection logic
   - Memory management
   - Context window optimization
   - Multi-agent coordination

2. **Tool Implementation**:
   - Custom tool creation
   - Tool validation
   - Error handling in tools
   - Tool composition
   - Async tool execution

3. **Memory Systems**:
   - Short-term memory
   - Long-term storage (vector DB)
   - Conversation summarization
   - Entity tracking
   - Memory retrieval strategies

4. **Prompt Engineering**:
   - System prompts
   - Few-shot examples
   - Chain-of-thought reasoning
   - Output formatting
   - Prompt templates

5. **RAG Integration**:
   - Document loading pipeline
   - Chunking strategies
   - Embedding generation
   - Vector store setup
   - Retrieval optimization

6. **Production Features**:
   - Streaming responses
   - Token counting
   - Cost tracking
   - Rate limiting
   - Fallback strategies

7. **Observability**:
   - LangSmith integration
   - Custom callbacks
   - Performance metrics
   - Decision tracking
   - Debug mode

Include error handling, testing strategies, and deployment considerations. Use the latest LangChain/LangGraph best practices.
