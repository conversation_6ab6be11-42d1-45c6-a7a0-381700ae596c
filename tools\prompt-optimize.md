---
model: sonnet
---

# AI 提示词优化

Optimize the following prompt for better AI model performance: $ARGUMENTS

Analyze and improve the prompt by:

1. **Prompt Engineering**:
   - Apply chain-of-thought reasoning
   - Add few-shot examples
   - Implement role-based instructions
   - Use clear delimiters and formatting
   - Add output format specifications

2. **Context Optimization**:
   - Minimize token usage
   - Structure information hierarchically
   - Remove redundant information
   - Add relevant context
   - Use compression techniques

3. **Performance Testing**:
   - Create prompt variants
   - Design evaluation criteria
   - Test edge cases
   - Measure consistency
   - Compare model outputs

4. **Model-Specific Optimization**:
   - GPT-4 best practices
   - Claude optimization techniques
   - Prompt chaining strategies
   - Temperature/parameter tuning
   - Token budget management

5. **RAG Integration**:
   - Context window management
   - Retrieval query optimization
   - Chunk size recommendations
   - Embedding strategies
   - Reranking approaches

6. **Production Considerations**:
   - Prompt versioning
   - A/B testing framework
   - Monitoring metrics
   - Fallback strategies
   - Cost optimization

Provide optimized prompts with explanations for each change. Include evaluation metrics and testing strategies. Consider both quality and cost efficiency.
