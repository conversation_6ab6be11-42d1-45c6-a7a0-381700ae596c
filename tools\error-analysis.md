---
model: sonnet
---

# 错误分析与解决

Analyze and resolve errors in: $ARGUMENTS

Perform comprehensive error analysis:

1. **Error Pattern Analysis**:
   - Categorize error types
   - Identify root causes
   - Trace error propagation
   - Analyze error frequency
   - Correlate with system events

2. **Debugging Strategy**:
   - Stack trace analysis
   - Variable state inspection
   - Execution flow tracing
   - Memory dump analysis
   - Race condition detection

3. **Error Handling Improvements**:
   - Custom exception classes
   - Error boundary implementation
   - Retry logic with backoff
   - Circuit breaker patterns
   - Graceful degradation

4. **Logging Enhancement**:
   - Structured logging setup
   - Correlation ID implementation
   - Log aggregation strategy
   - Debug vs production logging
   - Sensitive data masking

5. **Monitoring Integration**:
   - Sentry/Rollbar setup
   - Error alerting rules
   - Error dashboards
   - Trend analysis
   - SLA impact assessment

6. **Recovery Mechanisms**:
   - Automatic recovery procedures
   - Data consistency checks
   - Rollback strategies
   - State recovery
   - Compensation logic

7. **Prevention Strategies**:
   - Input validation
   - Type safety improvements
   - Contract testing
   - Defensive programming
   - Code review checklist

Provide specific fixes, preventive measures, and long-term reliability improvements. Include test cases for each error scenario.
